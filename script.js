// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 创建更多动态光粒子
    createDynamicParticles();
    
    // 添加鼠标交互效果
    addMouseInteraction();
    
    // 添加点击佛像的祝福效果
    addBlessingEffect();
    
    // 添加音效（可选）
    // addSoundEffects();
});

// 创建动态光粒子
function createDynamicParticles() {
    const container = document.querySelector('.light-particles');
    const particleCount = 20;
    
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'dynamic-particle';
        
        // 随机位置
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        
        // 随机大小
        const size = Math.random() * 6 + 2;
        
        // 随机动画延迟
        const delay = Math.random() * 6;
        
        particle.style.cssText = `
            position: absolute;
            left: ${x}%;
            top: ${y}%;
            width: ${size}px;
            height: ${size}px;
            background: radial-gradient(circle, #ffd700, #ffed4e);
            border-radius: 50%;
            box-shadow: 0 0 ${size * 2}px rgba(255, 215, 0, 0.8);
            animation: dynamicFloat ${4 + Math.random() * 4}s ease-in-out infinite;
            animation-delay: ${delay}s;
            opacity: 0.7;
        `;
        
        container.appendChild(particle);
    }
    
    // 添加动态浮动动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes dynamicFloat {
            0%, 100% { 
                transform: translateY(0px) translateX(0px) scale(1); 
                opacity: 0.7; 
            }
            25% { 
                transform: translateY(-15px) translateX(10px) scale(1.1); 
                opacity: 1; 
            }
            50% { 
                transform: translateY(-30px) translateX(-5px) scale(0.9); 
                opacity: 0.8; 
            }
            75% { 
                transform: translateY(-10px) translateX(-15px) scale(1.2); 
                opacity: 0.9; 
            }
        }
    `;
    document.head.appendChild(style);
}

// 添加鼠标交互效果
function addMouseInteraction() {
    const buddha = document.querySelector('.buddha');
    const container = document.querySelector('.container');
    
    container.addEventListener('mousemove', function(e) {
        const rect = container.getBoundingClientRect();
        const x = (e.clientX - rect.left) / rect.width;
        const y = (e.clientY - rect.top) / rect.height;
        
        // 根据鼠标位置调整金光强度
        const intensity = Math.sqrt(Math.pow(x - 0.5, 2) + Math.pow(y - 0.5, 2));
        const glowIntensity = Math.max(0.3, 1 - intensity);
        
        buddha.style.filter = `drop-shadow(0 0 ${30 * glowIntensity}px rgba(255, 215, 0, ${glowIntensity}))`;
        
        // 创建跟随鼠标的光点
        createMouseParticle(e.clientX, e.clientY);
    });
    
    // 鼠标离开时恢复默认效果
    container.addEventListener('mouseleave', function() {
        buddha.style.filter = 'drop-shadow(0 0 30px rgba(255, 215, 0, 0.8))';
    });
}

// 创建跟随鼠标的光点
function createMouseParticle(x, y) {
    const particle = document.createElement('div');
    particle.style.cssText = `
        position: fixed;
        left: ${x}px;
        top: ${y}px;
        width: 8px;
        height: 8px;
        background: radial-gradient(circle, #ffd700, transparent);
        border-radius: 50%;
        pointer-events: none;
        z-index: 1000;
        animation: fadeOut 1s ease-out forwards;
    `;
    
    document.body.appendChild(particle);
    
    // 1秒后移除粒子
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 1000);
    
    // 添加淡出动画
    if (!document.querySelector('#fadeOutStyle')) {
        const style = document.createElement('style');
        style.id = 'fadeOutStyle';
        style.textContent = `
            @keyframes fadeOut {
                0% { opacity: 1; transform: scale(1); }
                100% { opacity: 0; transform: scale(2); }
            }
        `;
        document.head.appendChild(style);
    }
}

// 添加点击佛像的祝福效果
function addBlessingEffect() {
    const buddha = document.querySelector('.buddha');
    const blessingTexts = [
        '南无阿弥陀佛',
        '愿您平安喜乐',
        '功德无量',
        '心想事成',
        '福慧双修',
        '吉祥如意'
    ];
    
    buddha.addEventListener('click', function(e) {
        // 创建祝福文字效果
        const blessing = document.createElement('div');
        const randomText = blessingTexts[Math.floor(Math.random() * blessingTexts.length)];
        
        blessing.textContent = randomText;
        blessing.style.cssText = `
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            color: #ffd700;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 0 20px rgba(255, 215, 0, 1);
            pointer-events: none;
            z-index: 1000;
            animation: blessingRise 3s ease-out forwards;
        `;
        
        document.querySelector('.buddha-container').appendChild(blessing);
        
        // 增强金光效果
        const lights = document.querySelectorAll('.golden-light');
        lights.forEach(light => {
            light.style.animation = 'pulse 0.5s ease-in-out';
        });
        
        // 恢复正常动画
        setTimeout(() => {
            lights.forEach((light, index) => {
                light.style.animation = `pulse 3s ease-in-out infinite`;
                light.style.animationDelay = `${index * 0.5}s`;
            });
        }, 500);
        
        // 3秒后移除祝福文字
        setTimeout(() => {
            if (blessing.parentNode) {
                blessing.parentNode.removeChild(blessing);
            }
        }, 3000);
    });
    
    // 添加祝福文字上升动画
    if (!document.querySelector('#blessingRiseStyle')) {
        const style = document.createElement('style');
        style.id = 'blessingRiseStyle';
        style.textContent = `
            @keyframes blessingRise {
                0% { 
                    opacity: 0; 
                    transform: translate(-50%, -50%) scale(0.5); 
                }
                20% { 
                    opacity: 1; 
                    transform: translate(-50%, -50%) scale(1.2); 
                }
                100% { 
                    opacity: 0; 
                    transform: translate(-50%, -150%) scale(1); 
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// 添加键盘交互
document.addEventListener('keydown', function(e) {
    if (e.code === 'Space') {
        e.preventDefault();
        // 空格键触发特殊光效
        triggerSpecialEffect();
    }
});

// 特殊光效
function triggerSpecialEffect() {
    const container = document.querySelector('.buddha-container');
    
    // 创建爆发式光效
    for (let i = 0; i < 12; i++) {
        const ray = document.createElement('div');
        const angle = (i * 30) * Math.PI / 180;
        
        ray.style.cssText = `
            position: absolute;
            left: 50%;
            top: 50%;
            width: 4px;
            height: 100px;
            background: linear-gradient(to top, transparent, #ffd700, transparent);
            transform-origin: bottom center;
            transform: translate(-50%, -100%) rotate(${i * 30}deg);
            animation: rayExpand 2s ease-out forwards;
            z-index: 2;
        `;
        
        container.appendChild(ray);
        
        // 2秒后移除光线
        setTimeout(() => {
            if (ray.parentNode) {
                ray.parentNode.removeChild(ray);
            }
        }, 2000);
    }
    
    // 添加光线扩展动画
    if (!document.querySelector('#rayExpandStyle')) {
        const style = document.createElement('style');
        style.id = 'rayExpandStyle';
        style.textContent = `
            @keyframes rayExpand {
                0% { 
                    opacity: 0; 
                    height: 0px; 
                }
                50% { 
                    opacity: 1; 
                    height: 200px; 
                }
                100% { 
                    opacity: 0; 
                    height: 300px; 
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
    const particles = document.querySelectorAll('.particle, .dynamic-particle');
    
    if (document.hidden) {
        // 页面隐藏时暂停动画以节省性能
        particles.forEach(particle => {
            particle.style.animationPlayState = 'paused';
        });
    } else {
        // 页面显示时恢复动画
        particles.forEach(particle => {
            particle.style.animationPlayState = 'running';
        });
    }
});

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    overflow: hidden;
    position: relative;
}

.container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    position: relative;
}

.background-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
    animation: backgroundPulse 4s ease-in-out infinite;
}

.buddha-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

/* 佛像主体 */
.buddha {
    position: relative;
    z-index: 5;
    filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.8));
}

.buddha-head {
    width: 120px;
    height: 140px;
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    border-radius: 50% 50% 45% 45%;
    position: relative;
    margin: 0 auto 10px;
    box-shadow: inset 0 10px 20px rgba(255, 255, 255, 0.3),
                inset 0 -10px 20px rgba(0, 0, 0, 0.1);
}

.buddha-face {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 60px;
}

.eye {
    width: 8px;
    height: 12px;
    background: #333;
    border-radius: 50%;
    position: absolute;
    top: 15px;
}

.left-eye { left: 20px; }
.right-eye { right: 20px; }

.nose {
    width: 6px;
    height: 8px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    position: absolute;
    top: 25px;
    left: 50%;
    transform: translateX(-50%);
}

.mouth {
    width: 20px;
    height: 8px;
    border: 2px solid #333;
    border-top: none;
    border-radius: 0 0 20px 20px;
    position: absolute;
    top: 35px;
    left: 50%;
    transform: translateX(-50%);
}

.hair-bun {
    width: 60px;
    height: 40px;
    background: linear-gradient(145deg, #4169e1, #6495ed);
    border-radius: 50%;
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
}

.buddha-body {
    width: 160px;
    height: 200px;
    background: linear-gradient(145deg, #ff6b35, #ff8c42);
    border-radius: 20px 20px 40px 40px;
    position: relative;
    margin: 0 auto;
    box-shadow: inset 0 10px 20px rgba(255, 255, 255, 0.2),
                inset 0 -10px 20px rgba(0, 0, 0, 0.1);
}

.robe {
    position: absolute;
    top: 20px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    border-radius: 15px 15px 30px 30px;
    opacity: 0.8;
}

.hands {
    position: absolute;
    top: 60px;
    width: 100%;
}

.left-hand, .right-hand {
    width: 30px;
    height: 40px;
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    border-radius: 50%;
    position: absolute;
}

.left-hand { left: -15px; }
.right-hand { right: -15px; }

.lotus-base {
    width: 200px;
    height: 40px;
    background: linear-gradient(145deg, #ff69b4, #ff1493);
    border-radius: 50%;
    margin: 10px auto 0;
    position: relative;
    box-shadow: 0 5px 15px rgba(255, 105, 180, 0.5);
}

/* 光环 */
.halo {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 3px solid transparent;
    border-radius: 50%;
    background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.8), transparent);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: rotate 8s linear infinite;
    z-index: 1;
}

/* 金光效果 */
.golden-light {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.6) 0%, transparent 70%);
    animation: pulse 3s ease-in-out infinite;
}

.light-1 {
    width: 300px;
    height: 300px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 0s;
}

.light-2 {
    width: 400px;
    height: 400px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 1s;
}

.light-3 {
    width: 500px;
    height: 500px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 2s;
}

.light-4 {
    width: 600px;
    height: 600px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 1.5s;
}

/* 光粒子 */
.light-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffd700;
    border-radius: 50%;
    box-shadow: 0 0 10px #ffd700;
    animation: float 6s ease-in-out infinite;
}

.particle-1 { top: 20%; left: 10%; animation-delay: 0s; }
.particle-2 { top: 30%; right: 15%; animation-delay: 1s; }
.particle-3 { top: 50%; left: 5%; animation-delay: 2s; }
.particle-4 { top: 70%; right: 10%; animation-delay: 3s; }
.particle-5 { top: 15%; left: 80%; animation-delay: 4s; }
.particle-6 { top: 60%; right: 5%; animation-delay: 5s; }
.particle-7 { top: 80%; left: 20%; animation-delay: 2.5s; }
.particle-8 { top: 40%; right: 25%; animation-delay: 3.5s; }

/* 底部文字 */
.blessing-text {
    position: absolute;
    bottom: 50px;
    text-align: center;
    z-index: 10;
}

.blessing-text p {
    color: #ffd700;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    animation: textGlow 2s ease-in-out infinite alternate;
}

/* 动画 */
@keyframes pulse {
    0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.8); }
    50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 1; }
    50% { transform: translateY(-20px) scale(1.2); opacity: 0.7; }
}

@keyframes backgroundPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

@keyframes textGlow {
    from { text-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
    to { text-shadow: 0 0 30px rgba(255, 215, 0, 1), 0 0 40px rgba(255, 215, 0, 0.8); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .buddha-head { width: 100px; height: 120px; }
    .buddha-body { width: 140px; height: 180px; }
    .lotus-base { width: 160px; }
    .halo { width: 160px; height: 160px; }
    .blessing-text p { font-size: 20px; }
}
